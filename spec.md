# 融氏古早味手工蘿蔔糕電商系統 - 專案規格文件

## 一、專案概述

### 1.1 專案簡介
融氏古早味手工蘿蔔糕是一個完整的電商網站系統，專門銷售手工製作的傳統蘿蔔糕產品。系統採用混合架構設計，結合現代化的前端技術與穩定的後端服務，提供從產品展示、線上訂購、訂單管理到客戶服務的完整電商解決方案。

### 1.2 專案目標
- 建立專業的蘿蔔糕品牌線上形象
- 提供便捷的線上訂購體驗
- 實現高效的訂單管理系統
- 整合多元化的配送與付款方式
- 建立完整的客戶關係管理系統
- 確保系統穩定性與資料安全性
- **統一前端架構**：整合 LINE LIFF 環境與一般瀏覽器的相容性，避免維護多個首頁檔案
- **提升開發效率**：建立單一入口點，減少重複開發與維護成本
- **LIFF 整合規劃**：將 index-liff.html 功能整合進 index.html，實現智慧環境偵測

### 1.3 目標使用者
#### 1.3.1 前台使用者
- 一般消費者：瀏覽產品、線上訂購
- 企業客戶：大量訂購、客製化需求
- 回購客戶：快速重複訂購

#### 1.3.2 後台使用者
- 店家管理員：訂單管理、系統設定
- 客服人員：客戶服務、訂單處理
- 配送人員：配送狀態更新

## 二、技術架構與規格

### 2.1 系統架構概述
系統採用混合架構設計，前端使用現代化的 React 技術棧，後端使用 PHP 提供 API 服務，資料儲存主要依賴 Google Sheets，並整合第三方服務如簡訊通知等。專案包含三個主要部分：
1. **前台電商網站**：面向消費者的產品展示與訂購系統
2. **LIFF 整合系統**：LINE 平台專用的訂購流程
3. **後台管理系統**：管理員使用的訂單與產品管理系統

### 2.2 前端技術架構
#### 2.2.1 核心技術棧
- **框架**：React 18.3.1 (CDN 載入，輕量化部署)
- **樣式框架**：TailwindCSS 4.1.8 + 自定義 CSS
- **圖標庫**：Font Awesome 6.5.1 (完整圖標支援)
- **狀態管理**：React Hooks + localStorage
- **表單處理**：原生 JavaScript 驗證
- **HTTP 客戶端**：Fetch API
- **TypeScript 支援**：TypeScript 5.4.5 (漸進式遷移)
- **建置工具**：Babel 7.27.4 + Vite 5.4.1 (後台)
- **LINE LIFF SDK**：LINE 平台整合與環境偵測
- **環境適配**：智慧偵測 LIFF 環境與一般瀏覽器
- **依賴管理**：npm + Composer (PHP)

#### 2.2.2 前端專案結構
```
前端檔案結構/
├── index.html              # 統一主頁面（整合 LIFF 相容性）
├── index-liff.html         # LIFF 專用頁面（待整合移除）
├── liff-order.html         # LIFF 訂單頁面
├── app.js                  # 主要應用邏輯（含 LIFF 整合）
├── styles/                 # 樣式檔案目錄
│   ├── main.css           # 主要樣式檔案
│   ├── header.css         # 頁首樣式
│   ├── hero.css           # 主視覺樣式
│   ├── products.css       # 產品展示樣式
│   ├── order.css          # 訂購表單樣式
│   ├── admin.css          # 後台管理樣式
│   ├── floating-buttons.css # 浮動按鈕樣式
│   └── tailwind.css       # TailwindCSS 編譯檔
├── components/             # React 組件目錄
│   ├── Header.js          # 頁首組件
│   ├── Hero.js            # 主視覺組件
│   ├── BrandStory.js      # 品牌故事組件
│   ├── ProductCard.js     # 產品卡片組件
│   ├── OrderForm.js       # 訂購表單組件（LIFF 相容）
│   ├── Footer.js          # 頁尾組件
│   ├── FloatingButtons.js # 浮動按鈕組件
│   └── admin/             # 後台管理組件
│       ├── Login.js       # 登入組件
│       └── AdminDashboard.js # 管理面板組件
├── utils/                  # 工具函數目錄
│   ├── cityDistricts.js   # 縣市地區資料
│   ├── orderUtils.js      # 訂單處理工具
│   ├── sheetsUtils.js     # Google Sheets 工具
│   ├── adminUtils.js      # 後台管理工具
│   └── errorUtils.js      # 錯誤處理工具
├── dist/                   # 編譯後檔案
└── images/                 # 產品與品牌圖片
```

### 2.3 後端技術架構
#### 2.3.1 核心技術
- **語言**：PHP 7.4+ (穩定版本，廣泛支援)
- **API 架構**：RESTful API 設計
- **資料儲存**：Google Sheets API v4 (主要) + localStorage (會話)
- **認證方式**：Google Service Account + PHP Session
- **依賴管理**：Composer

#### 2.3.2 後端專案結構
```
後端檔案結構/
├── admin.php                    # 管理後台主頁
├── admin-dashboard-wrapper.php # 後台框架頁面
├── api/
│   ├── submit_order.php        # 訂單提交 API
│   ├── get_orders.php          # 取得訂單列表
│   ├── update_order_status.php # 更新訂單狀態
│   ├── delete_order.php        # 刪除訂單
│   ├── get_delivery_dates.php  # 取得可配送日期
│   └── admin_login.php         # 管理員登入驗證
├── includes/
│   ├── config.php              # 系統設定檔
│   ├── functions.php           # 共用函數
│   └── auth.php               # 認證相關函數
├── backend-dashboard/          # React 後台 Dashboard
│   ├── src/
│   ├── package.json
│   └── vite.config.js
└── service-account-key.json    # Google API 認證金鑰
```

### 2.4 依賴管理
#### 2.4.1 前端依賴 (package.json)
- **React 生態系**：React 18.3.1, React DOM 18.3.1
- **TypeScript 支援**：TypeScript 5.4.5, @types/react, @types/react-dom
- **建置工具**：Babel 7.27.4, @babel/preset-env, @babel/preset-react, @babel/preset-typescript
- **樣式工具**：TailwindCSS 4.1.8, Autoprefixer 10.4.21, PostCSS 8.5.4
- **程式碼品質**：ESLint 8.57.0, TypeScript ESLint 7.7.1
- **開發工具**：@stagewise/toolbar 0.6.2, @stagewise-plugins/react 0.6.2

#### 2.4.2 後端依賴 (composer.json)
- **PHP 版本**：PHP 7.4+ | 8.0+
- **Google API**：google/apiclient 2.15 (Google Sheets API v4)
- **簡訊服務**：twilio/sdk 7.0 (三竹簡訊整合)
- **環境管理**：vlucas/phpdotenv 5.5
- **測試框架**：phpunit/phpunit 9.0 (開發環境)

### 2.5 第三方服務整合
#### 2.5.1 Google Sheets API
- **版本**：Google Sheets API v4
- **認證方式**：Service Account JSON 金鑰
- **權限範圍**：SPREADSHEETS (讀寫權限)
- **工作表結構**：
  - 主要訂單工作表 (15個欄位)
  - 客戶名單工作表 (8個欄位)
  - 配送日期設定工作表

#### 2.5.2 簡訊通知服務
- **服務商**：三竹簡訊 API (透過 Twilio SDK)
- **功能**：訂單確認簡訊自動發送
- **編碼**：UTF-8 支援中文簡訊
- **重試機制**：最多2次重試確保發送成功
- **防重複**：clientid 機制避免重複發送

### 2.5 資料庫設計 (Google Sheets)
#### 2.5.1 試算表結構
- **試算表 ID**：1BHlvdEp9FtJ8GcWQl2J_5YvMQNrXXXXXXXXXXXX (範例)
- **主要工作表**：
  - Sheet1：訂單資料 (15個欄位)
  - 客戶名單：客戶基本資料 (8個欄位)
  - 配送設定：可配送日期設定
  - 系統設定：系統參數設定

## 三、核心功能模組

### 3.1 前台電商功能
#### 3.1.1 品牌首頁展示
- **Hero 區塊**：主視覺與品牌標語
- **品牌故事**：融氏古早味的歷史與理念
- **產品展示**：三種主力產品介紹
- **製作流程**：手工製作過程說明
- **客戶評價**：真實客戶回饋展示
- **媒體報導**：相關新聞與報導
- **聯絡資訊**：多元聯繫方式
- **LIFF 環境適配**：智慧偵測 LINE 瀏覽器環境並自動調整 UI
- **統一體驗**：確保 LIFF 與一般瀏覽器使用體驗一致

#### 3.1.2 產品展示系統
- **三種產品**：原味蘿蔔糕 (NT$250)、芋頭粿 (NT$350)、台式鹹蘿蔔糕 (NT$350)
- **產品卡片**：圖片、價格、成分說明、特色介紹
- **運費規則**：滿額免運與運費計算說明
- **數量選擇器**：直觀的 +/- 按鈕操作

#### 3.1.3 產品資訊與使用指南
- **保存方式**：冷藏保存指南
- **拆封注意事項**：保存期限與品質維護
- **品質檢查標準**：產品品質保證說明
- **訂購指南**：完整的訂購流程說明

#### 3.1.4 智能訂購系統
- **智能數量選擇**：產品數量控制與即時計算
- **客戶資料管理**：完整的客戶資訊收集
- **智能配送選擇**：
  - 宅配到府：縣市/地區二級聯動選擇
  - 7-11超商取貨：整合門市選擇器
- **動態到貨日期**：後台可設定的可選日期
- **地址驗證**：即時地址格式檢查
- **聯絡方式整合**：Facebook/LINE 社群帳號
- **多元付款方式**：銀行轉帳/貨到付款
- **訂單確認系統**：詳細的訂單預覽與確認
- **Google Sheets 整合**：訂單資料即時同步
- **SMS 通知系統**：自動發送訂單確認簡訊
- **防重複提交**：IP 快取與時間限制機制
- **LIFF 整合功能**：
  - 環境智慧偵測：自動識別 LINE LIFF 環境
  - 條件式 SDK 載入：僅在 LIFF 環境載入相關資源
  - 統一表單處理：LIFF 與一般瀏覽器共用表單邏輯
  - LINE 使用者資訊整合：自動填入 LINE 使用者資料（如可用）

### 3.2 後台管理系統
#### 3.2.1 管理員認證系統
- **安全登入**：帳號密碼驗證機制
- **Session 管理**：自動登出與會話保護
- **權限控制**：管理員權限驗證

#### 3.2.2 訂單管理功能
- **訂單列表**：完整的訂單顯示與篩選
- **訂單搜尋**：按客戶姓名、電話、日期搜尋
- **狀態管理**：訂單狀態更新與追蹤
- **詳細檢視**：完整的訂單資訊查看
- **批量操作**：批量狀態更新與刪除
- **CSV 匯出**：訂單資料匯出功能

#### 3.2.3 配送日期管理
- **動態日期設定**：可設定可選的到貨日期
- **配送方式區分**：不同配送方式的日期設定
- **假日排除**：自動排除星期日等不配送日
- **即時更新**：前台即時同步可選日期

#### 3.2.4 Google Sheets 整合管理
- **API 連線測試**：測試 Google Sheets 連線狀態
- **資料同步狀態**：檢查資料同步情況
- **直接存取**：快速開啟 Google Sheets 查看
- **系統設定**：API 環境變數和憑證管理
- **依賴檢查**：Composer 套件狀態檢查
- **錯誤監控**：系統錯誤日誌與監控

#### 3.2.5 系統管理功能
- **響應式設計**：支援各種裝置的後台操作
- **HTTPS/HTTP 智能切換**：自動適應不同協議環境
- **跨域處理**：安全的跨域請求處理
- **效能監控**：系統運行狀態監控

### 3.3 Google Sheets 深度整合
#### 3.3.1 智能資料儲存
- **即時寫入**：訂單資料即時寫入 Google Sheets
- **雙重儲存**：訂單工作表 + 客戶名單工作表
- **資料格式化**：自動格式化電話號碼、地址、社群帳號
- **防重複機制**：IP 快取與時間限制防止重複提交
- **錯誤處理**：完整的錯誤處理與重試機制

#### 3.3.2 工作表結構優化
- **訂單工作表 (Sheet1)**：15個欄位的完整訂單資訊
- **客戶名單工作表**：8個欄位的客戶基本資料
- **自動行號管理**：智能行號分配與管理
- **時間戳記**：台北時區的精確時間記錄
- **地址智能處理**：宅配地址與超商門市地址的不同格式化

#### 3.3.3 SMS 通知系統
- **三竹簡訊整合**：完整的 SMS 發送功能
- **智能發送邏輯**：測試模式與正式模式自動切換
- **重試機制**：最多2次重試確保發送成功
- **電話號碼格式化**：台灣本地格式處理
- **訊息客製化**：包含訂單詳情的個人化簡訊
- **UTF-8編碼支援**：正確處理中文簡訊內容
- **防重複發送**：clientid機制避免重複發送
- **發送狀態追蹤**：完整的發送日誌與狀態記錄

#### 3.3.4 API 安全與效能
- **Service Account 認證**：安全的 Google API 存取
- **環境變數管理**：敏感資訊的安全儲存
- **CORS 處理**：跨域請求的安全處理
- **錯誤日誌**：詳細的錯誤記錄與追蹤
- **效能優化**：批量操作與快取機制

## 四、資料結構定義

### 4.1 前端資料結構
#### 4.1.1 訂單資料 (Order)
```typescript
interface Order {
  id?: string;                    // 訂單 ID (自動生成)
  orderNumber?: string;           // 訂單編號
  products: {                     // 產品數量
    radish: number;              // 原味蘿蔔糕數量
    taro: number;                // 芋頭粿數量
    hongkong: number;            // 台式鹹蘿蔔糕數量
  };
  shipping: number;               // 運費
  totalAmount: number;            // 總金額
  customerInfo: CustomerInfo;     // 客戶資訊
  status: OrderStatus;            // 訂單狀態
  orderDate: string;              // 訂購日期
  createdAt?: string;             // 建立時間戳
  updatedAt?: string;             // 更新時間戳
}
```

#### 4.1.2 客戶資訊 (CustomerInfo)
```typescript
interface CustomerInfo {
  name: string;                   // 客戶姓名
  phone: string;                  // 聯絡電話
  deliveryMethod: DeliveryMethod; // 配送方式
  district: string;               // 縣市
  area: string;                   // 地區
  address: string;                // 詳細地址
  preferredDate: string;          // 希望到貨日期
  preferredTime: string;          // 希望到貨時間
  contactMethod: ContactMethod;   // 聯絡方式
  socialAccount: string;          // 社群帳號
  paymentMethod: PaymentMethod;   // 付款方式
  notes?: string;                 // 備註
}
```

#### 4.1.3 產品資訊 (Product)
```typescript
interface Product {
  id: string;                     // 產品 ID
  name: string;                   // 產品名稱
  price: number;                  // 產品價格
  description: string;            // 產品描述
  image: string;                  // 產品圖片
  ingredients: string[];          // 成分列表
  features: string[];             // 產品特色
  weight: string;                 // 重量規格
  shelf_life: string;             // 保存期限
}
```

### 4.2 後端資料結構
#### 4.2.1 管理員資料 (Admin)
```php
class Admin {
    public string $username;        // 管理員帳號
    public string $password;        // 管理員密碼 (加密)
    public string $email;           // 管理員信箱
    public string $role;            // 管理員角色
    public DateTime $created_at;    // 建立時間
    public DateTime $last_login;    // 最後登入時間
    public bool $is_active;         // 帳號狀態
}
```

#### 4.2.2 系統設定 (SystemConfig)
```php
class SystemConfig {
    public array $delivery_dates;   // 可配送日期設定
    public array $delivery_areas;   // 配送地區設定
    public array $product_prices;   // 產品價格設定
    public int $shipping_threshold; // 免運門檻
    public int $shipping_fee;       // 運費設定
    public bool $sms_enabled;       // 簡訊功能開關
    public string $maintenance_mode; // 維護模式
}
```


### 4.3 Google Sheets 資料結構

#### 4.3.1 訂單工作表 (Sheet1) 欄位對應
| 欄位 | 說明 | 資料類型 | 範例 | 備註 |
|------|------|----------|------|------|
| A | 時間戳記 | DateTime | 2024-12-20 14:30:25 | 台北時區 |
| B | 客戶姓名 | String | 王小明 | 必填欄位 |
| C | 聯絡電話 | String | '0912345678 | 前綴單引號防格式化 |
| D | 配送方式 | String | 宅配到府 | 宅配到府/7-11超商取貨 |
| E | 配送地址 | String | 台北市信義區信義路五段7號 | 完整地址 |
| F | 希望到貨日期 | Date | 2024-12-21 | YYYY-MM-DD 格式 |
| G | 希望到貨時間 | String | 上午 (13點前) | 時段選擇 |
| H | 備註 | String | 請按門鈴 | 可選欄位 |
| I | 訂單項目 | String | 原味蘿蔔糕 x 2, 芋頭糕 x 1 | 格式化商品清單 |
| J | 總金額 | Number | 850 | 包含運費 |
| K | 聯絡方式 | String | LINE | Facebook/LINE/電話 |
| L | 社群帳號 | String | 'line123456 | 前綴單引號 |
| M | 付款方式 | String | 銀行轉帳 | 銀行轉帳/貨到付款 |
| N | 行號 | Number | 1 | 自動遞增 |
| O | 訂單狀態 | String | 訂單確認中 | 狀態管理 |
| P | 付款狀態 | String | 未收費 | 付款追蹤 (新增) |

#### 4.3.2 客戶名單工作表欄位對應
| 欄位 | 說明 | 資料類型 | 範例 | 備註 |
|------|------|----------|------|------|
| A | 客戶姓名 | String | 王小明 | 主要識別 |
| B | 聯絡電話 | String | '0912345678 | 唯一識別碼 |
| C | 配送方式 | String | 宅配到府 | 偏好配送方式 |
| D | 配送地址 | String | 台北市信義區信義路五段7號 | 常用地址 |
| E | 聯絡方式 | String | LINE | 偏好聯絡方式 |
| F | 社群帳號 | String | 'line123456 | 社群聯絡資訊 |
| G | 建立日期 | Date | 2024-12-20 | 首次訂購日期 |
| H | 訂單項目 | String | 原味蘿蔔糕 x 2, 芋頭糕 x 1 | 最近訂購項目 |
| I | 總訂單數 | Number | 5 | 累計訂單數量 (新增) |
| J | 總消費金額 | Number | 4250 | 累計消費金額 (新增) |

#### 4.3.3 配送設定工作表
| 欄位 | 說明 | 資料類型 | 範例 |
|------|------|----------|------|
| A | 配送方式 | String | 宅配到府 |
| B | 可配送日期 | Date | 2024-12-21 |
| C | 配送時段 | String | 上午 (13點前) |
| D | 是否啟用 | Boolean | TRUE |
| E | 備註 | String | 假日不配送 |


## 五、業務規則與邏輯

### 5.1 產品定價與運費規則
#### 5.1.1 產品定價
- **原味蘿蔔糕**：NT$250/條
- **芋頭粿**：NT$350/條
- **台式鹹蘿蔔糕**：NT$350/條
- **鳳梨豆腐乳**：NT$300/條 (季節限定)

#### 5.1.2 運費計算規則
- **基本運費**：NT$100
- **免運條件** (符合任一條件即免運)：
  - 購買總額 ≥ NT$350
  - 原味蘿蔔糕購買 ≥ 2條
  - 芋頭粿購買 ≥ 1條
  - 台式鹹蘿蔔糕購買 ≥ 1條
- **特殊優惠**：首次購買客戶免運費

#### 5.1.3 促銷活動規則
- **滿額贈送**：購買滿 NT$500 贈送小包裝試吃品
- **組合優惠**：三種口味各買一條享 9 折優惠
- **節慶優惠**：農曆新年期間全品項 95 折

### 5.2 訂單狀態管理
#### 5.2.1 訂單狀態流程
- **訂單確認中** (pending)：新訂單的初始狀態
- **已抄單** (confirmed)：訂單已確認，開始準備
- **製作中** (preparing)：商品製作階段
- **已出貨** (shipped)：商品已發送
- **已送達** (delivered)：客戶已收到商品
- **已完成** (completed)：訂單完全處理完畢
- **取消訂單** (cancelled)：訂單已取消
- **退貨處理** (returned)：退貨處理中

#### 5.2.2 付款狀態管理
- **未收費** (unpaid)：尚未收到款項
- **已收費** (paid)：已收到全額款項
- **待轉帳** (pending_transfer)：等待客戶轉帳
- **未全款** (partial_paid)：僅收到部分款項
- **退款中** (refunding)：退款處理中
- **已退款** (refunded)：已完成退款

### 5.3 配送規則
#### 5.3.1 配送方式
- **宅配到府**：
  - 需填寫完整地址 (縣市、地區、詳細地址)
  - 支援全台配送 (離島另計運費)
  - 配送時間：1-3個工作天
- **7-11超商取貨**：
  - 需選擇取貨門市
  - 支援全台 7-11 門市
  - 取貨期限：7天
- **門市自取**：
  - 需預約取貨時間
  - 營業時間：週一至週六 9:00-18:00
  - 地址：台北市大安區復興南路一段XXX號

#### 5.3.2 配送時段
- **上午時段**：09:00-13:00
- **下午時段**：13:00-18:00
- **晚上時段**：18:00-21:00 (限宅配)
- **假日配送**：週六正常配送，週日暫停

### 5.4 客戶服務規則
#### 5.4.1 退換貨政策
- **退貨期限**：收貨後 7 天內
- **退貨條件**：商品未拆封且包裝完整
- **不可退貨**：已拆封或過期商品
- **退款方式**：原付款方式退回

#### 5.4.2 品質保證
- **保存期限**：冷藏保存 7 天
- **品質問題**：免費更換或全額退款
- **配送損壞**：免費重新配送
- **客訴處理**：24小時內回應

## 六、使用者介面設計規範

### 6.1 響應式設計架構
#### 6.1.1 斷點設計
- **桌面端** (Desktop)：1200px 以上
  - 多欄位佈局，完整功能展示
  - 側邊欄導航，豐富的互動元素
- **平板端** (Tablet)：768px - 1199px
  - 兩欄佈局，適度簡化功能
  - 觸控友善的按鈕尺寸
- **手機端** (Mobile)：768px 以下
  - 單欄佈局，堆疊式設計
  - 大型觸控按鈕，簡化導航

#### 6.1.2 適應性設計原則
- **內容優先**：重要內容在小螢幕優先顯示
- **觸控友善**：按鈕最小尺寸 44px × 44px
- **載入優化**：行動裝置優先載入關鍵資源
- **字體縮放**：支援系統字體大小設定

### 6.2 視覺設計系統
#### 6.2.1 色彩配置
- **主色調**：
  - 品牌紅：#e53e3e (主要按鈕、連結)
  - 深紅色：#c53030 (懸停狀態)
  - 淺紅色：#fed7d7 (背景強調)
- **輔助色彩**：
  - 成功綠：#38a169 (成功訊息)
  - 警告橙：#dd6b20 (警告訊息)
  - 錯誤紅：#e53e3e (錯誤訊息)
  - 資訊藍：#3182ce (資訊訊息)
- **中性色彩**：
  - 背景色：#f7fafc (主背景)
  - 卡片背景：#ffffff (內容背景)
  - 邊框色：#e2e8f0 (分隔線)
  - 文字色：#2d3748 (主要文字)
  - 次要文字：#718096 (輔助文字)

#### 6.2.2 字體系統
- **字體家族**：
  - 系統字體：-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif
  - 中文字體："PingFang TC", "Microsoft JhengHei", "Noto Sans TC"
- **字體大小階層**：
  - 主標題：3rem (48px)
  - 次標題：2.25rem (36px)
  - 章節標題：1.875rem (30px)
  - 小標題：1.5rem (24px)
  - 內文：1rem (16px)
  - 小字：0.875rem (14px)
  - 極小字：0.75rem (12px)

#### 6.2.3 間距系統
- **基礎間距單位**：4px
- **常用間距**：
  - xs: 4px
  - sm: 8px
  - md: 16px
  - lg: 24px
  - xl: 32px
  - 2xl: 48px
  - 3xl: 64px

### 6.3 組件設計規範
#### 6.3.1 按鈕設計
- **主要按鈕**：品牌紅背景，白色文字
- **次要按鈕**：白色背景，品牌紅邊框
- **危險按鈕**：錯誤紅背景，白色文字
- **按鈕狀態**：正常、懸停、按下、禁用

#### 6.3.2 表單設計
- **輸入框**：圓角 4px，邊框 1px
- **標籤**：必填欄位標示紅色星號
- **驗證**：即時驗證與錯誤提示
- **提交**：防重複提交機制

#### 6.3.3 卡片設計
- **陰影**：box-shadow: 0 1px 3px rgba(0,0,0,0.12)
- **圓角**：border-radius: 8px
- **內距**：padding: 24px
- **懸停效果**：輕微陰影增強

## 七、安全性架構設計

### 7.1 前端安全機制
#### 7.1.1 輸入驗證與過濾
- **客戶端驗證**：即時表單驗證，提升使用者體驗
- **資料清理**：輸入內容過濾與 HTML 轉義
- **格式驗證**：電話號碼、電子郵件格式檢查
- **長度限制**：防止過長輸入造成系統負擔
- **LIFF 環境安全**：
  - LIFF SDK 來源驗證
  - LINE 平台身份驗證整合
  - 跨環境資料傳輸加密
  - LIFF 特定的 CSP 規則配置

#### 7.1.2 跨站腳本攻擊 (XSS) 防護
- **內容安全政策** (CSP)：限制腳本來源
- **輸出編碼**：動態內容 HTML 編碼
- **DOM 操作安全**：避免 innerHTML 直接賦值
- **第三方腳本**：僅載入信任的 CDN 資源

#### 7.1.3 跨站請求偽造 (CSRF) 防護
- **CSRF Token**：表單隱藏令牌驗證
- **SameSite Cookie**：限制跨站 Cookie 傳送
- **Referer 檢查**：驗證請求來源
- **雙重提交**：Cookie 與表單雙重驗證

### 7.2 後端安全架構
#### 7.2.1 認證與授權
- **密碼安全**：
  - 使用 password_hash() 加密存儲
  - 最小密碼強度要求
  - 登入失敗次數限制
  - Session 超時機制
- **權限控制**：
  - 基於角色的存取控制 (RBAC)
  - API 端點權限驗證
  - 敏感操作二次確認

#### 7.2.2 API 安全設計
- **CORS 設定**：
  - 嚴格的跨域來源控制
  - 預檢請求處理
  - 憑證傳送限制
- **請求驗證**：
  - HTTP 方法限制
  - Content-Type 檢查
  - 請求大小限制
  - 頻率限制 (Rate Limiting)

#### 7.2.3 資料保護
- **輸入過濾**：
  - SQL 注入防護 (雖使用 Google Sheets)
  - 特殊字符過濾
  - 檔案上傳限制
- **輸出安全**：
  - 敏感資訊遮罩
  - 錯誤訊息標準化
  - 日誌資料脫敏

### 7.3 第三方服務安全
#### 7.3.1 Google Sheets API 安全
- **Service Account 管理**：
  - JSON 金鑰檔案權限 600
  - 定期金鑰輪換
  - 最小權限原則
- **API 存取控制**：
  - IP 白名單限制
  - API 配額監控
  - 異常存取警報

#### 7.3.2 簡訊服務安全
- **憑證保護**：
  - 環境變數存儲
  - 加密傳輸
  - 存取日誌記錄
- **內容安全**：
  - 簡訊內容過濾
  - 發送頻率限制
  - 惡意內容檢測

### 7.4 網路安全
#### 7.4.1 傳輸安全
- **HTTPS 強制**：
  - 生產環境強制 SSL/TLS
  - HSTS 標頭設定
  - 安全 Cookie 設定
- **協議安全**：
  - TLS 1.2+ 版本要求
  - 強加密套件
  - 憑證有效性檢查

#### 7.4.2 伺服器安全
- **防火牆設定**：
  - 不必要端口關閉
  - IP 白名單管理
  - DDoS 防護
- **系統安全**：
  - 定期安全更新
  - 檔案權限管理
  - 系統監控

## 八、效能優化策略

### 8.1 前端效能優化
#### 8.1.1 資源載入優化
- **CDN 載入**：
  - React 18.2.0 從 unpkg CDN 載入
  - TailwindCSS 從 CDN 載入，減少打包大小
  - Font Awesome 圖標按需載入
- **資源壓縮**：
  - CSS/JS 檔案 Gzip 壓縮
  - 圖片 WebP 格式優先
  - SVG 圖標優化與壓縮
- **快取策略**：
  - 靜態資源長期快取 (1年)
  - HTML 檔案短期快取 (1小時)
  - API 回應適當快取 (5分鐘)

#### 8.1.2 渲染效能優化
- **關鍵渲染路徑**：
  - 內聯關鍵 CSS
  - 非關鍵 CSS 延遲載入
  - JavaScript 非阻塞載入
- **圖片優化**：
  - 響應式圖片 (srcset)
  - 懶載入 (Intersection Observer)
  - 圖片尺寸優化
- **字體優化**：
  - 字體預載入
  - font-display: swap
  - 系統字體優先

#### 8.1.3 JavaScript 效能
- **組件優化**：
  - React.memo 防止不必要重渲染
  - useCallback/useMemo 優化
  - 虛擬滾動 (長列表)
- **狀態管理**：
  - 最小化狀態更新
  - 批量狀態更新
  - 避免深層物件更新

### 8.2 後端效能優化
#### 8.2.1 API 效能優化
- **Google Sheets API**：
  - 批量讀寫操作
  - 範圍查詢優化
  - API 配額管理
- **快取機制**：
  - Redis/Memcached 快取 (如可用)
  - 檔案快取備選方案
  - 快取失效策略
- **資料庫優化**：
  - 查詢結果快取
  - 索引優化 (Google Sheets 限制內)
  - 分頁查詢

#### 8.2.2 伺服器效能
- **PHP 優化**：
  - OPcache 啟用
  - 記憶體限制優化
  - 執行時間控制
- **併發處理**：
  - 連線池管理
  - 非阻塞 I/O
  - 請求佇列

### 8.3 網路效能優化
#### 8.3.1 傳輸優化
- **壓縮傳輸**：
  - Gzip/Brotli 壓縮
  - 資料最小化
  - JSON 結構優化
- **連線優化**：
  - HTTP/2 支援
  - Keep-Alive 連線
  - 連線復用

#### 8.3.2 CDN 與快取
- **CDN 策略**：
  - 靜態資源 CDN 分發
  - 地理位置就近存取
  - 邊緣快取
- **瀏覽器快取**：
  - Cache-Control 標頭
  - ETag 驗證
  - 版本控制

### 8.4 監控與分析
#### 8.4.1 效能監控
- **前端監控**：
  - Core Web Vitals 追蹤
  - 載入時間分析
  - 使用者體驗指標
- **後端監控**：
  - API 回應時間
  - 錯誤率統計
  - 資源使用率

#### 8.4.2 效能分析工具
- **開發工具**：
  - Chrome DevTools
  - Lighthouse 分析
  - WebPageTest
- **監控服務**：
  - Google Analytics
  - 自定義效能追蹤
  - 錯誤監控

## 九、瀏覽器相容性與支援

### 9.1 目標瀏覽器支援
#### 9.1.1 桌面瀏覽器
- **Google Chrome 90+**：完整功能支援，主要測試環境
- **Mozilla Firefox 88+**：完整功能支援，次要測試環境
- **Safari 14+**：完整功能支援，macOS 用戶
- **Microsoft Edge 90+**：完整功能支援，Windows 用戶
- **Internet Explorer**：不支援 (已停止支援)

#### 9.1.2 行動瀏覽器
- **iOS Safari 14+**：完整行動體驗，iPhone/iPad 用戶
- **Android Chrome 90+**：完整行動體驗，Android 用戶
- **Samsung Internet 13+**：基本功能支援
- **UC Browser**：基本功能支援

#### 9.1.3 LINE 平台環境
- **LINE 內建瀏覽器**：iOS/Android 最新版本，完整功能支援
- **LIFF 環境**：支援 LIFF SDK 2.0+，完整 LIFF 功能整合
- **LINE 應用程式**：iOS 11.2.5+ / Android 7.0+
- **跨平台相容性**：確保 LIFF 與一般瀏覽器功能一致性
- **環境自動檢測**：智慧識別 LINE 環境並自動適配

### 9.2 功能相容性策略
#### 9.2.1 漸進式增強
- **基礎功能**：所有支援瀏覽器都能使用核心功能
- **增強功能**：現代瀏覽器享有更好的使用體驗
- **優雅降級**：舊版瀏覽器功能適度簡化

#### 9.2.2 Polyfill 策略
- **必要 Polyfill**：
  - Fetch API (IE/舊版 Safari)
  - Promise (IE)
  - Array.from/includes (IE)
- **條件載入**：僅在需要時載入 Polyfill
- **體積控制**：最小化 Polyfill 檔案大小

### 9.3 降級處理機制
#### 9.3.1 JavaScript 禁用處理
- **基本表單功能**：純 HTML 表單提交
- **無 JavaScript 提示**：引導用戶啟用 JavaScript
- **伺服器端渲染**：關鍵內容伺服器端生成

#### 9.3.2 網路不穩定處理
- **離線檢測**：navigator.onLine 狀態檢查
- **重試機制**：自動重試失敗的請求
- **快取策略**：Service Worker 離線快取 (漸進式)
- **使用者提示**：網路狀態友善提示

### 9.4 測試策略
#### 9.4.1 跨瀏覽器測試
- **自動化測試**：Selenium WebDriver 跨瀏覽器測試
- **手動測試**：關鍵功能人工驗證
- **視覺回歸測試**：UI 一致性檢查

#### 9.4.2 行動裝置測試
- **實機測試**：主流 iOS/Android 裝置
- **模擬器測試**：開發階段快速驗證
- **響應式測試**：各種螢幕尺寸適配

## 十、部署架構與維運

### 10.1 部署架構設計
#### 10.1.1 混合部署模式
- **前端部署**：
  - 靜態檔案 (HTML/CSS/JS) 部署至 Web 伺服器
  - CDN 加速靜態資源載入
  - 支援 SPA 路由 (.htaccess 設定)
- **後端部署**：
  - PHP API 服務部署至應用伺服器
  - Google Sheets API 整合
  - SMS 服務整合
- **資料層**：
  - Google Sheets 作為主要資料儲存
  - localStorage 作為客戶端暫存

#### 10.1.2 環境配置
- **開發環境** (Development)：
  - 本地 XAMPP/WAMP 環境
  - 測試用 Google Sheets
  - 簡訊測試模式
- **測試環境** (Staging)：
  - 生產環境鏡像
  - 完整功能測試
  - 效能測試
- **生產環境** (Production)：
  - 高可用性配置
  - 安全性強化
  - 監控與備份

### 10.2 伺服器環境需求
#### 10.2.1 硬體需求
- **最低配置**：
  - CPU: 2 核心
  - RAM: 4GB
  - 儲存: 50GB SSD
  - 頻寬: 100Mbps
- **建議配置**：
  - CPU: 4 核心
  - RAM: 8GB
  - 儲存: 100GB SSD
  - 頻寬: 1Gbps

#### 10.2.2 軟體需求
- **作業系統**：
  - Linux (Ubuntu 20.04+ / CentOS 8+)
  - Windows Server 2019+
- **Web 伺服器**：
  - Apache 2.4+ (mod_rewrite 啟用)
  - Nginx 1.18+ (備選)
- **PHP 環境**：
  - PHP 7.4+ (建議 8.0+)
  - 必要擴展：curl, json, openssl, mbstring
  - Composer 2.0+
- **SSL 憑證**：
  - Let's Encrypt (免費)
  - 商業 SSL 憑證 (建議)

### 10.3 部署流程
#### 10.3.1 自動化部署
```bash
# 部署腳本範例
#!/bin/bash

# 1. 拉取最新代碼
git pull origin main

# 2. 安裝/更新依賴
composer install --no-dev --optimize-autoloader

# 3. 設定檔案權限
chmod 644 *.php
chmod 600 service-account-key.json
chmod 755 cache/

# 4. 清除快取
rm -rf cache/*.json

# 5. 重啟服務
sudo systemctl reload apache2
```

#### 10.3.2 環境變數設定
```bash
# .env 檔案範例
GOOGLE_SHEETS_ID="your_sheets_id"
SMS_API_KEY="your_sms_api_key"
SMS_API_SECRET="your_sms_secret"
ADMIN_USERNAME="admin"
ADMIN_PASSWORD_HASH="$2y$10$..."
ENVIRONMENT="production"
DEBUG_MODE="false"
```

### 10.4 監控與維運
#### 10.4.1 系統監控
- **伺服器監控**：
  - CPU/記憶體使用率
  - 磁碟空間監控
  - 網路流量分析
- **應用監控**：
  - API 回應時間
  - 錯誤率統計
  - 使用者活動追蹤
- **第三方服務監控**：
  - Google Sheets API 配額
  - SMS 服務狀態
  - CDN 服務狀態

#### 10.4.2 日誌管理
- **錯誤日誌**：
  - PHP 錯誤日誌
  - Apache/Nginx 錯誤日誌
  - 應用程式錯誤日誌
- **存取日誌**：
  - Web 伺服器存取日誌
  - API 呼叫日誌
  - 使用者行為日誌
- **日誌輪轉**：
  - 定期壓縮舊日誌
  - 自動清理過期日誌
  - 重要日誌備份

#### 10.4.3 備份策略
- **程式碼備份**：
  - Git 版本控制
  - 定期程式碼備份
- **資料備份**：
  - Google Sheets 自動備份
  - 定期匯出重要資料
- **設定備份**：
  - 環境變數備份
  - 伺服器設定備份

#### 10.4.4 災難復原
- **復原計畫**：
  - RTO (復原時間目標): 4小時
  - RPO (復原點目標): 1小時
- **備援機制**：
  - 資料庫備援
  - 服務備援
- **測試計畫**：
  - 定期災難復原演練
  - 備份資料驗證

## 十一、API 介面規格

### 11.1 API 設計原則
- **RESTful 設計**：遵循 REST 架構原則
- **統一回應格式**：標準化的 JSON 回應結構
- **錯誤處理**：完整的錯誤碼與訊息
- **版本控制**：API 版本管理策略

### 11.2 API 端點列表
#### 11.2.1 訂單相關 API
```
POST /api/submit_order.php      # 提交新訂單
GET  /api/get_orders.php        # 取得訂單列表
PUT  /api/update_order_status.php # 更新訂單狀態
DELETE /api/delete_order.php    # 刪除訂單
```

#### 11.2.2 管理相關 API
```
POST /api/admin_login.php       # 管理員登入
GET  /api/get_delivery_dates.php # 取得可配送日期
POST /api/set_delivery_dates.php # 設定可配送日期
```

### 11.3 API 回應格式
#### 11.3.1 成功回應
```json
{
  "success": true,
  "data": {
    "orders": [...],
    "total": 100,
    "page": 1
  },
  "message": "操作成功",
  "timestamp": "2024-12-20T14:30:25+08:00"
}
```

#### 11.3.2 錯誤回應
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "輸入資料驗證失敗",
    "details": {
      "phone": "電話號碼格式不正確"
    }
  },
  "timestamp": "2024-12-20T14:30:25+08:00"
}
```

## 十二、開發規範與流程

### 12.1 代碼規範
#### 12.1.1 PHP 代碼規範
- **PSR-12 標準**：遵循 PHP-FIG PSR-12 代碼風格
- **命名規範**：
  - 類別：PascalCase (OrderManager)
  - 方法：camelCase (submitOrder)
  - 變數：camelCase ($orderData)
  - 常數：UPPER_SNAKE_CASE (MAX_ORDER_ITEMS)

#### 12.1.2 JavaScript 代碼規範
- **ES6+ 語法**：使用現代 JavaScript 語法
- **命名規範**：
  - 函數：camelCase (submitOrder)
  - 變數：camelCase (orderData)
  - 常數：UPPER_SNAKE_CASE (API_BASE_URL)
  - 組件：PascalCase (OrderForm)
- **LIFF 整合規範**：
  - LIFF 相關函數：liff 前綴 (liffInit, liffGetProfile)
  - 環境檢測：統一使用 detectEnvironment 函數
  - 條件載入：使用 Promise 處理異步載入
  - 錯誤處理：LIFF 錯誤統一處理機制

```javascript
// 函數命名：駝峰式命名
function calculateShippingFee(orderAmount, region) {
    // 函數內容
}

// 常數命名：全大寫加底線
const API_BASE_URL = 'https://api.example.com';
const MAX_ORDER_QUANTITY = 100;
const LIFF_ID = 'your-liff-id';

// 物件屬性：駝峰式命名
const orderData = {
    customerName: '王小明',
    phoneNumber: '0912345678',
    deliveryAddress: '台北市信義區',
    isLiffEnvironment: false
};

// LIFF 環境檢測：統一函數
function detectLiffEnvironment() {
    return window.location.search.includes('liff.state') || 
           navigator.userAgent.includes('Line');
}

// 條件式 LIFF 初始化
async function initializeLiff() {
    if (detectLiffEnvironment() && window.liff) {
        try {
            await liff.init({ liffId: LIFF_ID });
            return true;
        } catch (error) {
            console.error('LIFF 初始化失敗:', error);
            return false;
        }
    }
    return false;
}

// 錯誤處理：統一格式
try {
    const response = await fetch(API_BASE_URL + '/orders');
    const data = await response.json();
} catch (error) {
    console.error('API 請求失敗:', error);
    showErrorMessage('系統暫時無法處理您的請求，請稍後再試');
}
```

#### 12.1.3 CSS 代碼規範
- **BEM 方法論**：Block__Element--Modifier
- **TailwindCSS 優先**：優先使用 Utility Classes
- **自定義 CSS**：僅在必要時使用

### 12.2 版本控制
#### 12.2.1 Git 工作流程
- **分支策略**：
  - main: 生產環境代碼
  - develop: 開發環境代碼
  - feature/*: 功能開發分支
  - hotfix/*: 緊急修復分支

#### 12.2.2 提交規範
- **Conventional Commits**：
  - feat: 新功能
  - fix: 錯誤修復
  - docs: 文件更新
  - style: 代碼格式
  - refactor: 重構
  - test: 測試
  - chore: 建置/工具

### 12.3 測試策略
#### 12.3.1 測試類型
- **單元測試**：PHPUnit (PHP) / Jest (JavaScript)
- **整合測試**：API 端點測試
- **E2E 測試**：Selenium WebDriver
- **效能測試**：Apache Bench / JMeter
- **LIFF 環境測試**：
  - LINE 內建瀏覽器相容性測試
  - LIFF SDK 功能測試
  - 跨環境一致性測試
  - 環境自動檢測功能測試

#### 12.3.2 測試覆蓋率
- **目標覆蓋率**：80% 以上
- **關鍵功能**：100% 覆蓋率
- **測試報告**：自動生成測試報告

## 十三、LINE LIFF 整合架構規劃

### 13.1 整合目標與背景
#### 13.1.1 現況分析
目前專案採用多檔案 LIFF 架構：
- **index.html**：一般瀏覽器環境使用的主頁面
- **liff-order.html**：LINE LIFF 環境專用的訂購頁面
- **liff-simple.html**：LIFF 簡化版頁面

這種分離架構導致以下問題：
- **維護成本高**：任何功能更新需要同時修改多個檔案
- **程式碼重複**：大部分內容和邏輯完全相同
- **版本不一致風險**：容易造成不同版本功能差異
- **開發效率低**：需要重複測試和部署
- **用戶體驗不一致**：不同環境可能有不同的功能表現

#### 13.1.2 整合目標
- **統一入口點**：將 LIFF 相關功能整合到 index.html 中
- **智能環境檢測**：自動檢測 LINE LIFF 環境並適配相應功能
- **向下相容**：確保現有功能在整合後正常運作
- **效能優化**：條件式載入 LIFF SDK，減少不必要的資源載入
- **維護簡化**：單一檔案維護，降低開發成本
- **體驗統一**：確保 LIFF 與一般瀏覽器功能一致性

### 13.2 技術整合方案
#### 13.2.1 環境檢測機制
```javascript
// 智能環境檢測函數
function detectEnvironment() {
    const isLineEnvironment = /Line/i.test(navigator.userAgent) ||
                             window.location.href.includes('liff://') ||
                             window.location.search.includes('liff_id') ||
                             window.location.hash.includes('liff');

    const isLiffApp = window.location.pathname.includes('liff') ||
                      window.location.search.includes('liff_id') ||
                      window.location.search.includes('liff.state');

    return {
        isLine: isLineEnvironment,
        isLiff: isLiffApp,
        userAgent: navigator.userAgent,
        url: window.location.href,
        referrer: document.referrer
    };
}
```

#### 13.2.2 條件式資源載入
```javascript
// LIFF SDK 條件載入
function loadLiffSDK() {
    return new Promise((resolve, reject) => {
        const environment = detectEnvironment();
        
        if (environment.isLine || environment.isLiff) {
            const script = document.createElement('script');
            script.charset = 'utf-8';
            script.src = 'https://static.line-scdn.net/liff/edge/2/sdk.js';
            script.onload = () => {
                console.log('✅ LIFF SDK 載入完成');
                resolve(true);
            };
            script.onerror = () => {
                console.warn('⚠️ LIFF SDK 載入失敗');
                resolve(false);
            };
            document.head.appendChild(script);
        } else {
            console.log('🌐 一般瀏覽器環境，跳過 LIFF SDK');
            resolve(false);
        }
    });
}
```

#### 13.2.3 統一載入指示器
```css
/* 通用載入指示器樣式 */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    flex-direction: column;
}

.loading-spinner {
    border: 3px solid #f3f4f6;
    border-top: 3px solid #ef4444;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}
```

### 13.3 整合實施計畫
#### 13.3.1 階段一：環境檢測整合（第1週）
**目標**：在 index.html 中加入環境檢測機制

**實施步驟**：
1. 在 index.html 的 `<head>` 區段加入環境檢測腳本
2. 實作條件式 LIFF SDK 載入邏輯（目前已部分實現）
3. 整合 liff-order.html 的 LIFF 專用組件到主系統
4. 加入載入指示器 HTML 結構和 CSS 樣式
5. 測試環境檢測準確性

**驗收標準**：
- 能正確檢測 LINE 瀏覽器環境
- 能正確檢測 LIFF 應用環境
- 一般瀏覽器不載入 LIFF SDK
- 載入指示器正常顯示和隱藏
- LIFF 專用組件正常運作

#### 13.3.2 階段二：應用程式邏輯整合（第2週）
**目標**：整合 React 應用程式的初始化邏輯

**實施步驟**：
1. 將 index-liff.html 的 App 組件邏輯移植到 app.js
2. 實作 LIFF 初始化的條件邏輯
3. 整合錯誤處理和載入狀態管理
4. 統一路由處理機制

**驗收標準**：
- LIFF 環境下應用程式正常初始化
- 一般瀏覽器環境功能不受影響
- 錯誤處理機制完整
- 路由切換正常運作

#### 13.3.3 階段三：樣式和相容性優化（第3週）
**目標**：確保 LINE 瀏覽器的樣式相容性

**實施步驟**：
1. 整合 LINE 瀏覽器特殊樣式處理
2. 優化觸控操作體驗
3. 測試各種螢幕尺寸適配
4. 驗證滾動和互動行為

**驗收標準**：
- LINE 瀏覽器中樣式顯示正常
- 觸控操作流暢
- 響應式設計在 LINE 環境正常
- 無樣式衝突或顯示異常

#### 13.3.4 階段四：測試和部署（第4週）
**目標**：全面測試和正式部署

**實施步驟**：
1. 跨環境功能測試（一般瀏覽器、LINE、LIFF）
2. 效能測試和優化
3. 使用者體驗測試
4. 正式部署和監控

**驗收標準**：
- 所有環境功能正常
- 效能指標符合要求
- 使用者體驗良好
- 監控系統正常運作

### 13.4 風險評估與應對
#### 13.4.1 技術風險
**風險**：LIFF SDK 載入失敗或相容性問題
**應對**：
- 實作完整的錯誤處理和降級機制
- 提供純 Web 版本作為備選方案
- 建立詳細的錯誤日誌和監控

**風險**：環境檢測不準確
**應對**：
- 多重檢測條件確保準確性
- 提供手動切換模式選項
- 建立使用者回饋機制

#### 13.4.2 業務風險
**風險**：整合過程中影響現有功能
**應對**：
- 採用漸進式整合策略
- 保留原始檔案作為備份
- 建立完整的回滾計畫

**風險**：使用者體驗下降
**應對**：
- 充分的使用者測試
- 效能監控和優化
- 快速回應使用者反饋

### 13.5 整合後的檔案結構
#### 13.5.1 主要檔案變更
```
專案根目錄/
├── index.html                 # 統一的主頁面（整合 LIFF 功能）
├── liff-order.html           # LIFF 專用訂購頁面（待整合）
├── liff-simple.html          # LIFF 簡化版頁面（待整合）
├── app.js                    # 整合後的主應用邏輯
├── components/
│   ├── liff/                 # LIFF 專用組件（已實現）
│   │   ├── Step1ProductSelection.js # 產品選擇步驟
│   │   ├── Step2CustomerInfo.js     # 客戶資訊步驟
│   │   ├── Step3OrderConfirm.js     # 訂單確認步驟
│   │   ├── CustomAlert.js           # LIFF 專用警告組件
│   │   └── ProgressIndicator.js     # 進度指示器
│   └── Alert/                # 通用警告組件
├── utils/
│   ├── environmentDetector.js # 環境檢測工具（待開發）
│   ├── liffManager.js        # LIFF 管理工具（待開發）
│   └── loadingManager.js     # 載入狀態管理（待開發）
└── styles/
    ├── main.css              # 主要樣式
    ├── liff-order.css        # LIFF 專用樣式（已實現）
    ├── line-compatibility.css # LINE 瀏覽器相容樣式（待開發）
    └── loading.css           # 載入指示器樣式（待開發）
```

#### 13.5.2 新增工具模組規劃
**environmentDetector.js**：環境檢測和管理
**liffManager.js**：LIFF 相關功能封裝
**loadingManager.js**：統一的載入狀態管理
**line-compatibility.css**：LINE 瀏覽器特殊樣式處理

### 13.6 效益評估
#### 13.6.1 開發效益
- **維護成本降低 50%**：單一檔案維護
- **開發效率提升 30%**：避免重複開發
- **測試時間減少 40%**：統一測試流程
- **部署複雜度降低**：單一部署目標

#### 13.6.2 使用者體驗效益
- **載入速度優化**：條件式資源載入
- **功能一致性**：統一的功能體驗
- **相容性提升**：更好的跨環境支援
- **維護品質**：減少版本差異問題

#### 13.6.3 技術債務清理
- **程式碼重複消除**：DRY 原則實踐
- **架構簡化**：清晰的單一入口點
- **文件統一**：單一技術文件維護
- **版本控制簡化**：減少分支管理複雜度

### 13.7 後續優化方向
#### 13.7.1 進階 LIFF 功能整合
- LIFF Profile API 整合
- LINE Pay 支付整合
- LINE Messaging API 整合
- 社群分享功能增強

#### 13.7.2 效能持續優化
- Service Worker 離線支援
- 資源預載入策略
- 圖片懶載入優化
- 程式碼分割和按需載入

#### 13.7.3 監控和分析
- 環境使用統計分析
- 效能指標監控
- 使用者行為分析
- 錯誤追蹤和報告

---

## 十四、參考文件與資源

### 14.1 技術文件
- [Google Sheets API v4 文件](https://developers.google.com/sheets/api)
- [三竹簡訊 API 文件](https://sms.mitake.com.tw/)
- [React 官方文件](https://reactjs.org/docs/)
- [TailwindCSS 文件](https://tailwindcss.com/docs)
- [PHP 官方文件](https://www.php.net/docs.php)
- [LINE LIFF 官方文件](https://developers.line.biz/en/docs/liff/)
- [LINE Messaging API 文件](https://developers.line.biz/en/docs/messaging-api/)

### 14.2 開發工具
- **IDE**: Visual Studio Code / PhpStorm
- **版本控制**: Git / GitHub
- **API 測試**: Postman / Insomnia
- **效能分析**: Chrome DevTools / Lighthouse
- **LIFF 測試**: LINE Developers Console
- **跨瀏覽器測試**: BrowserStack / Sauce Labs

### 14.3 相關標準
- **Web 標準**: W3C HTML5 / CSS3
- **無障礙**: WCAG 2.1 AA
- **安全標準**: OWASP Top 10
- **效能標準**: Core Web Vitals
- **LINE 平台標準**: LINE Design System
- **行動裝置標準**: PWA Guidelines

---
**文件版本**：v3.0
**最後更新**：2025-08-02
**維護者**：開發團隊

本文件為專案開發規格依據，後續如有異動請同步更新。所有功能開發與系統架構設計均應遵循本規格文件的規範與標準。

## 更新記錄
- **v3.0 (2025-08-02)**：
  - 更新實際技術架構與依賴版本
  - 新增 LIFF 整合規劃章節
  - 完善 API 端點列表
  - 更新專案檔案結構
  - 移除無用檔案與資料夾
